<!-- Main OpenVidu session -->
<opv-session
  #ovSessionComponent
  [sessionName]="mySessionId"
  [user]="myUserName"
  [tokens]="tokens"
  [ovSettings]="ovSettings"
  (sessionCreated)="handlerSessionCreatedEvent($event)"
  (participantCreated)="handlerPublisherCreatedEvent($event)"
  (error)="handlerErrorEvent($event)"
  (toolbarButtonClicked)="onToolbarButtonClicked($event)"
  *ngIf="session && sessionJoined">
</opv-session>





